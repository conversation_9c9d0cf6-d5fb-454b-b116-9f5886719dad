# AI Search Engine

A modern AI-powered search engine similar to Perplexity, built with Next.js, OpenAI, and real-time web search capabilities.

## Features

- 🔍 **Real-time Web Search**: Searches the web and fetches content from multiple sources
- 🤖 **AI-Powered Responses**: Uses OpenAI GPT-4 to synthesize information from search results
- 💡 **Smart Recommendations**: Powered by <PERSON><PERSON><PERSON> for intelligent content suggestions
- 📚 **Interactive Citations**: Clickable citations with hover previews of source content
- 🌊 **Streaming Responses**: Real-time streaming of AI responses for better user experience
- 🌙 **Dark/Light Mode**: Toggle between dark and light themes
- 📱 **Responsive Design**: Works seamlessly on desktop and mobile devices
- 🕒 **Search History**: Keeps track of recent searches with local storage
- 🎨 **Modern UI**: Clean, intuitive interface inspired by Perplexity

## Tech Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, OpenAI API, Admesh API
- **Web Scraping**: Axios, Cheerio
- **Icons**: Lucide React
- **Styling**: Tailwind CSS with custom gradients and animations

## Getting Started

### Prerequisites

- Node.js 18+
- OpenAI API key
- Admesh API key (optional, for smart recommendations)

### Installation

1. Clone the repository:
```bash
git clone <your-repo-url>
cd ai-search-engine
```

2. Install dependencies:
```bash
npm install
```

3. Create a `.env.local` file in the root directory:
```bash
# Required
OPENAI_API_KEY=your_openai_api_key_here

# Optional - for smart recommendations
ADMESH_API_KEY=your_admesh_api_key_here
ADMESH_BASE_URL=https://api.useadmesh.com
```

> **Note**: You can copy `.env.example` to `.env.local` and fill in your API keys.

4. Run the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
ai-search-engine/
├── src/
│   ├── app/
│   │   ├── api/
│   │   │   └── search/
│   │   │       ├── route.ts          # Standard search API
│   │   │       └── stream/
│   │   │           └── route.ts      # Streaming search API
│   │   ├── globals.css               # Global styles
│   │   ├── layout.tsx                # Root layout
│   │   └── page.tsx                  # Main search interface
│   ├── components/
│   │   ├── CitationRenderer.tsx      # Interactive citation component
│   │   ├── SearchHistory.tsx         # Search history component
│   │   └── ThemeToggle.tsx           # Dark/light mode toggle
│   └── lib/
│       ├── ai.ts                     # AI response generation utilities
│       ├── admesh.ts                 # Admesh integration for smart recommendations
│       └── search.ts                 # Web search utilities
├── .env.local                        # Environment variables
└── package.json
```

## API Endpoints

### POST /api/search
Standard search endpoint that returns complete results.

**Request:**
```json
{
  "query": "How does quantum computing work?"
}
```

**Response:**
```json
{
  "query": "How does quantum computing work?",
  "aiResponse": "Quantum computing is a revolutionary computing paradigm...",
  "sources": [
    {
      "id": 1,
      "title": "Quantum Computing Explained",
      "url": "https://example.com",
      "snippet": "Quantum computing uses quantum mechanics...",
      "favicon": "https://example.com/favicon.ico"
    }
  ]
}
```

### POST /api/search/stream
Streaming search endpoint for real-time responses.

**Request:**
```json
{
  "query": "How does quantum computing work?"
}
```

**Response:** Server-Sent Events (SSE) stream with:
- `status`: Search progress updates
- `sources`: Search results
- `recommendations`: Smart recommendations from Admesh
- `content`: Streaming AI response chunks
- `complete`: End of stream signal

## Features in Detail

### Smart Recommendations
- Powered by Admesh API for intelligent content suggestions
- Contextual recommendations based on search queries
- Fallback recommendations when Admesh is unavailable
- Category-based organization with relevance scores

### Interactive Citations
- Hover over citations to see source previews
- Click citations to visit original sources
- Visual indicators for source reliability

### Search History
- Automatically saves recent searches
- Quick access to previous queries
- Clear individual items or entire history

### Dark Mode
- System preference detection
- Manual toggle option
- Persistent theme selection

### Responsive Design
- Mobile-first approach
- Adaptive layouts for all screen sizes
- Touch-friendly interactions

## Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add your environment variables:
   - `OPENAI_API_KEY` (required)
   - `ADMESH_API_KEY` (optional)
4. Deploy automatically

### Other Platforms
The app can be deployed on any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `OPENAI_API_KEY` | Your OpenAI API key | Yes |
| `ADMESH_API_KEY` | Your Admesh API key for smart recommendations | No |
| `ADMESH_BASE_URL` | Admesh API base URL (defaults to https://api.useadmesh.com) | No |

### Getting API Keys

1. **OpenAI API Key**:
   - Visit [OpenAI Platform](https://platform.openai.com/api-keys)
   - Create an account and generate an API key

2. **Admesh API Key** (Optional):
   - Visit [Admesh Documentation](https://docs.useadmesh.com/)
   - Sign up for an account and get your API key
   - Without this key, the app will show fallback recommendations

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Acknowledgments

- Inspired by Perplexity AI
- Built with Next.js and OpenAI
- Icons by Lucide React
