# 🚀 AI Search Engine Setup Guide

This guide will help you set up all the necessary API keys and configuration for your AI search engine with Admesh smart recommendations.

## 📋 Prerequisites

- Node.js 18+ installed
- npm or yarn package manager
- Git (for version control)

## 🔑 Required API Keys

### 1. OpenAI API Key (REQUIRED) ✅

**Status**: Already configured in your `.env.local` file!

Your OpenAI API key is needed for:
- AI-powered search responses
- Content synthesis and analysis
- Streaming AI responses

**If you need to get a new key:**
1. Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. Sign up or log in to your account
3. Click "Create new secret key"
4. Copy the key (starts with `sk-`)
5. Add it to `.env.local` as `OPENAI_API_KEY=your_key_here`

### 2. Admesh API Key (OPTIONAL but RECOMMENDED)

**Status**: Not configured yet - follow steps below to add smart recommendations

Admesh provides:
- Smart product recommendations
- Contextual advertising
- Revenue generation through recommendations
- Enhanced user experience

**To get your Admesh API key:**

1. **Visit Admesh Website**
   ```
   https://useadmesh.com
   ```

2. **Sign Up for Free Account**
   - Click "Sign Up" or "Get Started"
   - Complete the registration process
   - Verify your email address

3. **Access Your Dashboard**
   - Log in to your account
   - Navigate to the API section or Dashboard
   - Look for "API Keys" or "Integration" section

4. **Generate API Key**
   - Click "Generate New API Key" or similar
   - Copy the generated key
   - Save it securely

5. **Add to Environment File**
   - Open your `.env.local` file
   - Uncomment the line: `# ADMESH_API_KEY=your_admesh_api_key_here`
   - Replace `your_admesh_api_key_here` with your actual key
   - Save the file

   Example:
   ```bash
   ADMESH_API_KEY=admesh_1234567890abcdef
   ```

## 🛠️ Setup Instructions

### 1. Clone and Install Dependencies

```bash
# Navigate to your project directory
cd ai-search-engine

# Install dependencies (if not already done)
npm install
```

### 2. Environment Configuration

Your `.env.local` file should look like this:

```bash
# OpenAI API Configuration (REQUIRED) ✅
OPENAI_API_KEY=**************************************************************************************************************************************************

# Admesh API Configuration (OPTIONAL)
ADMESH_API_KEY=your_admesh_api_key_here  # Add your key here
ADMESH_BASE_URL=https://api.useadmesh.com

# Application Configuration
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 3. Start the Application

```bash
# Start the development server
npm run dev
```

The application will be available at: `http://localhost:3000` (or `http://localhost:3002` if 3000 is in use)

## 🧪 Testing the Integration

### With Admesh API Key:
1. Perform a search query
2. You should see:
   - AI-powered search results
   - Smart product recommendations in the sidebar
   - Inline recommendation ads
   - Floating recommendation widget

### Without Admesh API Key:
1. The app will still work perfectly
2. You'll see fallback recommendations instead of Admesh-powered ones
3. All other features remain functional

## 🔧 Troubleshooting

### Common Issues:

1. **OpenAI API Errors**
   - Check if your API key is valid
   - Ensure you have sufficient credits
   - Verify the key starts with `sk-`

2. **Admesh Not Working**
   - Check if `ADMESH_API_KEY` is uncommented in `.env.local`
   - Verify the API key is correct
   - Check browser console for error messages

3. **Port Already in Use**
   - The app will automatically use the next available port
   - Check the terminal output for the correct URL

### Environment Variables Checklist:

- [ ] `OPENAI_API_KEY` - ✅ Already configured
- [ ] `ADMESH_API_KEY` - Add your Admesh key here
- [ ] `ADMESH_BASE_URL` - ✅ Already configured
- [ ] `NODE_ENV` - ✅ Already configured

## 📞 Support

- **OpenAI Issues**: [OpenAI Help Center](https://help.openai.com/)
- **Admesh Issues**: [Admesh Documentation](https://docs.useadmesh.com/)
- **Application Issues**: Check the browser console and terminal logs

## 🎉 You're All Set!

Once you've added your Admesh API key, your AI search engine will have:
- Intelligent search responses
- Smart product recommendations
- Multiple ad formats
- Revenue generation potential
- Enhanced user experience

Happy searching! 🔍✨
