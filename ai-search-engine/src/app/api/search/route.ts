import { NextRequest, NextResponse } from 'next/server';
import { searchWeb, fetchPageContent } from '@/lib/search';
import { generateAIResponse } from '@/lib/ai';

export async function POST(request: NextRequest) {
  try {
    const { query } = await request.json();

    if (!query || typeof query !== 'string') {
      return NextResponse.json(
        { error: 'Query is required and must be a string' },
        { status: 400 }
      );
    }

    // Search the web
    const searchResults = await searchWeb(query);

    // Fetch content from top results
    const resultsWithContent = await Promise.all(
      searchResults.slice(0, 3).map(async (result) => {
        const content = await fetchPageContent(result.url);
        return { ...result, content };
      })
    );

    // Generate AI response using the improved utility
    const aiResponse = await generateAIResponse({
      query,
      sources: resultsWithContent
    });

    return NextResponse.json({
      query,
      aiResponse,
      sources: searchResults.map((result, index) => ({
        id: index + 1,
        title: result.title,
        url: result.url,
        snippet: result.snippet,
        favicon: result.favicon
      }))
    });

  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
