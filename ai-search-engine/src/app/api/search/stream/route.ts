import { NextRequest } from 'next/server';
import { searchWeb, fetchPageContent } from '@/lib/search';
import { generateStreamingAIResponse } from '@/lib/ai';

export async function POST(request: NextRequest) {
  const { query } = await request.json();

  if (!query || typeof query !== 'string') {
    return new Response('Query is required', { status: 400 });
  }

  const encoder = new TextEncoder();
  
  const stream = new ReadableStream({
    async start(controller) {
      try {
        // Send initial status
        controller.enqueue(
          encoder.encode(`data: ${JSON.stringify({ type: 'status', message: 'Searching the web...' })}\n\n`)
        );

        // Search the web
        const searchResults = await searchWeb(query);
        
        // Send search results
        controller.enqueue(
          encoder.encode(`data: ${JSON.stringify({
            type: 'sources',
            data: searchResults.map((result, index) => ({
              id: index + 1,
              title: result.title,
              url: result.url,
              snippet: result.snippet,
              favicon: result.favicon
            }))
          })}\n\n`)
        );

        // Update status
        controller.enqueue(
          encoder.encode(`data: ${JSON.stringify({ type: 'status', message: 'Analyzing content...' })}\n\n`)
        );

        // Fetch content from top results
        const resultsWithContent = await Promise.all(
          searchResults.slice(0, 3).map(async (result) => {
            const content = await fetchPageContent(result.url);
            return { ...result, content };
          })
        );

        // Update status
        controller.enqueue(
          encoder.encode(`data: ${JSON.stringify({ type: 'status', message: 'Generating AI response...' })}\n\n`)
        );

        // Stream AI response using the improved utility
        const streamGenerator = generateStreamingAIResponse({
          query,
          sources: resultsWithContent
        });

        for await (const content of streamGenerator) {
          controller.enqueue(
            encoder.encode(`data: ${JSON.stringify({ type: 'content', data: content })}\n\n`)
          );
        }

        // Send completion signal
        controller.enqueue(
          encoder.encode(`data: ${JSON.stringify({ type: 'complete' })}\n\n`)
        );

      } catch (error) {
        console.error('Streaming error:', error);
        controller.enqueue(
          encoder.encode(`data: ${JSON.stringify({ type: 'error', message: 'An error occurred' })}\n\n`)
        );
      } finally {
        controller.close();
      }
    }
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    },
  });
}
