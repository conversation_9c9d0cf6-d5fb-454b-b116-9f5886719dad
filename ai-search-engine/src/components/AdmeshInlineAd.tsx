'use client';

import React from 'react';
import { 
  AdMeshSimpleAd,
  AdMeshProductCard,
  AdMeshBadge,
  type AdMeshRecommendation,
  type AdMeshTheme 
} from 'admesh-ui-sdk';
import { Star, ExternalLink, Zap } from 'lucide-react';

interface AdmeshResponse {
  recommendations: AdmeshRecommendation[];
  query: string;
  success: boolean;
  error?: string;
}

interface AdmeshInlineAdProps {
  admeshResponse: AdmeshResponse;
  adType?: 'simple' | 'product-card' | 'banner' | 'compact';
  theme?: AdMeshTheme;
  showBadge?: boolean;
  maxRecommendations?: number;
  onRecommendationClick?: (adId: string, admeshLink: string) => void;
}

// Transform our recommendation format to AdMesh format using actual API response data
const transformRecommendations = (recommendations: any[]): AdMeshRecommendation[] => {
  return recommendations.map((rec, index) => ({
    title: rec.title,
    reason: rec.description,
    intent_match_score: rec.relevanceScore || 0.8,
    admesh_link: rec.url || `https://useadmesh.com/track?ad_id=${rec.title.toLowerCase().replace(/\s+/g, '-')}-${index}`,
    ad_id: rec.ad_id || `${rec.title.toLowerCase().replace(/\s+/g, '-')}-${index}`,
    product_id: rec.product_id || rec.title.toLowerCase().replace(/\s+/g, '-'),
    features: rec.features || (rec.category ? [rec.category] : []),
    has_free_tier: rec.has_free_tier || true,
    trial_days: rec.trial_days || 14,
    keywords: rec.keywords || [rec.category || 'General'],
    categories: rec.categories || [rec.category || 'General'],
    pricing: rec.pricing || rec.reward_note || '',
    description: rec.description || rec.reason || 'No description available',
    url: rec.redirect_url || rec.url || rec.admesh_link
  }));
};

export default function AdmeshInlineAd({
  admeshResponse,
  adType = 'simple',
  theme = { mode: 'light' },
  showBadge = true,
  maxRecommendations = 1,
  onRecommendationClick
}: AdmeshInlineAdProps) {
  if (!admeshResponse.success || !admeshResponse.recommendations.length) {
    return null;
  }

  const transformedRecommendations = transformRecommendations(
    admeshResponse.recommendations.slice(0, maxRecommendations)
  );

  const handleRecommendationClick = (adId: string, admeshLink: string) => {
    if (onRecommendationClick) {
      onRecommendationClick(adId, admeshLink);
    } else {
      window.open(admeshLink, '_blank');
    }
  };

  const renderBadge = () => {
    if (!showBadge) return null;

    return (
      <div className="flex justify-center mb-3">
        <AdMeshBadge
          type="Popular"
          variant="primary"
          size="sm"
        />
      </div>
    );
  };

  switch (adType) {
    case 'simple':
      return (
        <div className="my-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          {renderBadge()}
          {transformedRecommendations.map((rec, index) => (
            <AdMeshSimpleAd
              key={index}
              recommendation={rec}
              theme={theme}
              compact={true}
              onClick={handleRecommendationClick}
            />
          ))}
        </div>
      );

    case 'product-card':
      return (
        <div className="my-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          {renderBadge()}
          {transformedRecommendations.map((rec, index) => (
            <AdMeshProductCard
              key={index}
              recommendation={rec}
              theme={theme}
              showMatchScore={true}
              onClick={handleRecommendationClick}
            />
          ))}
        </div>
      );

    case 'banner':
      const topRec = transformedRecommendations[0];
      return (
        <div className="my-6 p-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg shadow-lg">
          {showBadge && (
            <div className="flex items-center justify-between mb-3">
              <span className="text-xs font-medium bg-white/20 px-2 py-1 rounded">
                Sponsored
              </span>
              <Zap className="w-4 h-4" />
            </div>
          )}
          
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <h3 className="font-semibold text-lg mb-1">{topRec.title}</h3>
              <p className="text-sm opacity-90 mb-2">{topRec.reason}</p>
              <div className="flex items-center text-xs">
                <Star className="w-3 h-3 mr-1" />
                <span>{Math.round(topRec.intent_match_score * 100)}% match</span>
              </div>
            </div>
            
            <button
              onClick={() => handleRecommendationClick(topRec.ad_id, topRec.admesh_link)}
              className="ml-4 bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors flex items-center"
            >
              Learn More
              <ExternalLink className="w-4 h-4 ml-1" />
            </button>
          </div>
        </div>
      );

    case 'compact':
      const compactRec = transformedRecommendations[0];
      return (
        <div className="my-4 p-3 bg-gray-50 dark:bg-slate-700 border-l-4 border-blue-500 rounded-r-lg">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              {showBadge && (
                <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                  Sponsored
                </span>
              )}
              <h4 className="font-medium text-gray-800 dark:text-white text-sm">
                {compactRec.title}
              </h4>
              <p className="text-xs text-gray-600 dark:text-gray-300 mt-1">
                {compactRec.reason}
              </p>
            </div>
            
            <button
              onClick={() => handleRecommendationClick(compactRec.ad_id, compactRec.admesh_link)}
              className="ml-3 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
            >
              <ExternalLink className="w-4 h-4" />
            </button>
          </div>
        </div>
      );

    default:
      return null;
  }
}
