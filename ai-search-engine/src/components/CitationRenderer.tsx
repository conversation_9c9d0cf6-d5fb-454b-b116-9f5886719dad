'use client';

import { useState } from 'react';
import { ExternalLink } from 'lucide-react';

interface Source {
  id: number;
  title: string;
  url: string;
  snippet: string;
  favicon?: string;
}

interface CitationRendererProps {
  content: string;
  sources: Source[];
}

export default function CitationRenderer({ content, sources }: CitationRendererProps) {
  const [hoveredCitation, setHoveredCitation] = useState<number | null>(null);

  // Function to render content with interactive citations
  const renderContentWithCitations = (text: string) => {
    const citationPattern = /\[(\d+)\]/g;
    const parts = [];
    let lastIndex = 0;
    let match;

    while ((match = citationPattern.exec(text)) !== null) {
      // Add text before citation
      if (match.index > lastIndex) {
        parts.push(text.slice(lastIndex, match.index));
      }

      const citationNumber = parseInt(match[1]);
      const source = sources.find(s => s.id === citationNumber);

      if (source) {
        parts.push(
          <span
            key={`citation-${match.index}`}
            className="relative inline-block"
            onMouseEnter={() => setHoveredCitation(citationNumber)}
            onMouseLeave={() => setHoveredCitation(null)}
          >
            <a
              href={source.url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors cursor-pointer"
            >
              [{citationNumber}]
            </a>
            
            {/* Citation tooltip */}
            {hoveredCitation === citationNumber && (
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-80 bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-600 rounded-lg shadow-lg p-4 z-10">
                <div className="flex items-start space-x-3">
                  {source.favicon && (
                    <img 
                      src={source.favicon} 
                      alt="" 
                      className="w-4 h-4 mt-1 flex-shrink-0"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  )}
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-gray-900 dark:text-white text-sm line-clamp-2 mb-1">
                      {source.title}
                    </h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-3 mb-2">
                      {source.snippet}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500 dark:text-gray-500 truncate">
                        {new URL(source.url).hostname}
                      </span>
                      <ExternalLink className="w-3 h-3 text-gray-400" />
                    </div>
                  </div>
                </div>
                {/* Tooltip arrow */}
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-200 dark:border-t-slate-600"></div>
              </div>
            )}
          </span>
        );
      } else {
        parts.push(match[0]); // Keep original citation if source not found
      }

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text
    if (lastIndex < text.length) {
      parts.push(text.slice(lastIndex));
    }

    return parts;
  };

  // Split content into paragraphs and render each with citations
  const paragraphs = content.split('\n\n').filter(p => p.trim());

  return (
    <div className="space-y-4">
      {paragraphs.map((paragraph, index) => (
        <p key={index} className="text-gray-700 dark:text-gray-300 leading-relaxed">
          {renderContentWithCitations(paragraph)}
        </p>
      ))}
    </div>
  );
}
