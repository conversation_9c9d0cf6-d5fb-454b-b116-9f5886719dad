'use client';

import { useState, useEffect } from 'react';
import { Clock, X } from 'lucide-react';

interface SearchHistoryItem {
  id: string;
  query: string;
  timestamp: number;
}

interface SearchHistoryProps {
  onSelectQuery: (query: string) => void;
  currentQuery: string;
}

export default function SearchHistory({ onSelectQuery, currentQuery }: SearchHistoryProps) {
  const [history, setHistory] = useState<SearchHistoryItem[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Load search history from localStorage
    const savedHistory = localStorage.getItem('searchHistory');
    if (savedHistory) {
      try {
        setHistory(JSON.parse(savedHistory));
      } catch (error) {
        console.error('Error loading search history:', error);
      }
    }
  }, []);

  useEffect(() => {
    // Save current query to history when it changes (and is not empty)
    if (currentQuery.trim() && currentQuery.length > 2) {
      const newItem: SearchHistoryItem = {
        id: Date.now().toString(),
        query: currentQuery.trim(),
        timestamp: Date.now()
      };

      setHistory(prev => {
        // Remove duplicate if exists
        const filtered = prev.filter(item => item.query !== newItem.query);
        // Add new item at the beginning and limit to 10 items
        const updated = [newItem, ...filtered].slice(0, 10);
        
        // Save to localStorage
        localStorage.setItem('searchHistory', JSON.stringify(updated));
        
        return updated;
      });
    }
  }, [currentQuery]);

  const clearHistory = () => {
    setHistory([]);
    localStorage.removeItem('searchHistory');
  };

  const removeItem = (id: string) => {
    setHistory(prev => {
      const updated = prev.filter(item => item.id !== id);
      localStorage.setItem('searchHistory', JSON.stringify(updated));
      return updated;
    });
  };

  const formatTimestamp = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  if (history.length === 0) return null;

  return (
    <div className="relative">
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
      >
        <Clock className="w-4 h-4" />
        <span>Recent searches</span>
      </button>

      {isVisible && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-600 rounded-lg shadow-lg z-10 max-h-80 overflow-y-auto">
          <div className="p-3 border-b border-gray-200 dark:border-slate-600 flex items-center justify-between">
            <h3 className="font-medium text-gray-900 dark:text-white">Recent Searches</h3>
            <button
              onClick={clearHistory}
              className="text-xs text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
            >
              Clear all
            </button>
          </div>
          
          <div className="py-2">
            {history.map((item) => (
              <div
                key={item.id}
                className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors group"
              >
                <button
                  onClick={() => {
                    onSelectQuery(item.query);
                    setIsVisible(false);
                  }}
                  className="flex-1 text-left"
                >
                  <div className="text-sm text-gray-900 dark:text-white truncate">
                    {item.query}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {formatTimestamp(item.timestamp)}
                  </div>
                </button>
                
                <button
                  onClick={() => removeItem(item.id)}
                  className="opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-all"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
