import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import AdmeshRecommendations from '../AdmeshRecommendations';

// Mock the admesh-ui-sdk
jest.mock('admesh-ui-sdk', () => ({
  AdMeshLayout: ({ recommendations, onProductClick }: any) => (
    <div data-testid="admesh-layout">
      {recommendations.map((rec: any, index: number) => (
        <div key={index} data-testid={`recommendation-${index}`}>
          <h3>{rec.title}</h3>
          <p>{rec.reason}</p>
          <button onClick={() => onProductClick(rec.ad_id, rec.admesh_link)}>
            Click
          </button>
        </div>
      ))}
    </div>
  ),
  AdMeshProductCard: ({ recommendation, onClick }: any) => (
    <div data-testid="admesh-product-card">
      <h3>{recommendation.title}</h3>
      <p>{recommendation.reason}</p>
      <button onClick={() => onClick(recommendation.ad_id, recommendation.admesh_link)}>
        Click
      </button>
    </div>
  ),
  AdMeshInlineRecommendation: ({ recommendation, onClick }: any) => (
    <div data-testid="admesh-inline-recommendation">
      <h3>{recommendation.title}</h3>
      <p>{recommendation.reason}</p>
      <button onClick={() => onClick(recommendation.ad_id, recommendation.admesh_link)}>
        Click
      </button>
    </div>
  ),
  AdMeshConversationalUnit: ({ recommendations, onRecommendationClick }: any) => (
    <div data-testid="admesh-conversational-unit">
      {recommendations.map((rec: any, index: number) => (
        <div key={index}>
          <h3>{rec.title}</h3>
          <button onClick={() => onRecommendationClick(rec.ad_id, rec.admesh_link)}>
            Click
          </button>
        </div>
      ))}
    </div>
  ),
  AdMeshCitationUnit: ({ recommendations, onRecommendationClick }: any) => (
    <div data-testid="admesh-citation-unit">
      {recommendations.map((rec: any, index: number) => (
        <div key={index}>
          <h3>{rec.title}</h3>
          <button onClick={() => onRecommendationClick(rec.ad_id, rec.admesh_link)}>
            Click
          </button>
        </div>
      ))}
    </div>
  )
}));

const mockAdmeshResponse = {
  recommendations: [
    {
      title: 'Test Product 1',
      description: 'This is a test product description',
      category: 'Software',
      relevanceScore: 0.95,
      url: 'https://example.com/product1'
    },
    {
      title: 'Test Product 2',
      description: 'Another test product description',
      category: 'Tools',
      relevanceScore: 0.87,
      url: 'https://example.com/product2'
    }
  ],
  query: 'test query',
  success: true
};

describe('AdmeshRecommendations', () => {
  it('renders successfully with cards display mode', () => {
    render(
      <AdmeshRecommendations
        admeshResponse={mockAdmeshResponse}
        displayMode="cards"
      />
    );

    expect(screen.getByText('Smart Recommendations')).toBeInTheDocument();
    expect(screen.getByTestId('admesh-layout')).toBeInTheDocument();
    expect(screen.getByText('Test Product 1')).toBeInTheDocument();
    expect(screen.getByText('Test Product 2')).toBeInTheDocument();
  });

  it('renders with inline display mode', () => {
    render(
      <AdmeshRecommendations
        admeshResponse={mockAdmeshResponse}
        displayMode="inline"
      />
    );

    expect(screen.getByText('Smart Recommendations')).toBeInTheDocument();
    expect(screen.getAllByTestId('admesh-inline-recommendation')).toHaveLength(2);
  });

  it('renders with conversational display mode', () => {
    render(
      <AdmeshRecommendations
        admeshResponse={mockAdmeshResponse}
        displayMode="conversational"
      />
    );

    expect(screen.getByTestId('admesh-conversational-unit')).toBeInTheDocument();
  });

  it('renders with citation display mode', () => {
    render(
      <AdmeshRecommendations
        admeshResponse={mockAdmeshResponse}
        displayMode="citation"
      />
    );

    expect(screen.getByTestId('admesh-citation-unit')).toBeInTheDocument();
  });

  it('displays error message when present', () => {
    const errorResponse = {
      ...mockAdmeshResponse,
      error: 'Test error message'
    };

    render(
      <AdmeshRecommendations
        admeshResponse={errorResponse}
        displayMode="cards"
      />
    );

    expect(screen.getByText('Test error message')).toBeInTheDocument();
  });

  it('does not render when unsuccessful and no recommendations', () => {
    const emptyResponse = {
      recommendations: [],
      query: 'test query',
      success: false
    };

    const { container } = render(
      <AdmeshRecommendations
        admeshResponse={emptyResponse}
        displayMode="cards"
      />
    );

    expect(container.firstChild).toBeNull();
  });

  it('calls onRecommendationClick when recommendation is clicked', () => {
    const mockOnClick = jest.fn();

    render(
      <AdmeshRecommendations
        admeshResponse={mockAdmeshResponse}
        displayMode="cards"
        onRecommendationClick={mockOnClick}
      />
    );

    const clickButton = screen.getAllByText('Click')[0];
    clickButton.click();

    expect(mockOnClick).toHaveBeenCalledWith(
      'test-product-1-0',
      'https://example.com/product1'
    );
  });
});
