import { admeshService } from '../admesh';

// Mock the Admesh module
jest.mock('admesh', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => ({
      recommend: {
        getRecommendations: jest.fn()
      }
    }))
  };
});

describe('AdmeshService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset environment variables
    delete process.env.ADMESH_API_KEY;
  });

  describe('getRecommendations', () => {
    it('should return error when API key is not configured', async () => {
      const result = await admeshService.getRecommendations('test query');
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('not initialized');
      expect(result.recommendations).toEqual([]);
    });

    it('should return recommendations when API key is configured', async () => {
      // Set up environment variable
      process.env.ADMESH_API_KEY = 'test-api-key';
      
      // Mock successful response
      const mockRecommendations = [
        {
          title: 'Test Recommendation',
          description: 'Test description',
          url: 'https://example.com',
          category: 'Test',
          score: 0.95
        }
      ];

      // Re-import to get fresh instance with API key
      jest.resetModules();
      const { admeshService: freshService } = require('../admesh');
      
      // Mock the client method
      const mockClient = {
        recommend: {
          getRecommendations: jest.fn().mockResolvedValue({
            recommendations: mockRecommendations
          })
        }
      };
      
      // Replace the client
      (freshService as any).client = mockClient;
      (freshService as any).isInitialized = true;

      const result = await freshService.getRecommendations('test query');
      
      expect(result.success).toBe(true);
      expect(result.recommendations).toHaveLength(1);
      expect(result.recommendations[0].title).toBe('Test Recommendation');
    });

    it('should handle API errors gracefully', async () => {
      process.env.ADMESH_API_KEY = 'test-api-key';
      
      jest.resetModules();
      const { admeshService: freshService } = require('../admesh');
      
      const mockClient = {
        recommend: {
          getRecommendations: jest.fn().mockRejectedValue(new Error('API Error'))
        }
      };
      
      (freshService as any).client = mockClient;
      (freshService as any).isInitialized = true;

      const result = await freshService.getRecommendations('test query');
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('API Error');
      expect(result.recommendations).toEqual([]);
    });
  });

  describe('getRecommendationsWithFallback', () => {
    it('should return fallback recommendations when Admesh fails', async () => {
      const result = await admeshService.getRecommendationsWithFallback('test query');
      
      expect(result.success).toBe(true);
      expect(result.recommendations).toHaveLength(3);
      expect(result.recommendations[0].title).toContain('test query');
      expect(result.error).toContain('Admesh unavailable');
    });

    it('should return Admesh recommendations when available', async () => {
      process.env.ADMESH_API_KEY = 'test-api-key';
      
      jest.resetModules();
      const { admeshService: freshService } = require('../admesh');
      
      const mockClient = {
        recommend: {
          getRecommendations: jest.fn().mockResolvedValue({
            recommendations: [{
              title: 'Admesh Recommendation',
              description: 'From Admesh API'
            }]
          })
        }
      };
      
      (freshService as any).client = mockClient;
      (freshService as any).isInitialized = true;

      const result = await freshService.getRecommendationsWithFallback('test query');
      
      expect(result.success).toBe(true);
      expect(result.recommendations[0].title).toBe('Admesh Recommendation');
      expect(result.error).toBeUndefined();
    });
  });

  describe('isAvailable', () => {
    it('should return false when not initialized', () => {
      expect(admeshService.isAvailable()).toBe(false);
    });

    it('should return true when properly initialized', () => {
      process.env.ADMESH_API_KEY = 'test-api-key';
      
      jest.resetModules();
      const { admeshService: freshService } = require('../admesh');
      
      (freshService as any).client = {};
      (freshService as any).isInitialized = true;

      expect(freshService.isAvailable()).toBe(true);
    });
  });
});
