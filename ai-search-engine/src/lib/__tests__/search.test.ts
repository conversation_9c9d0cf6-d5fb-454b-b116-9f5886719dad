import { searchWeb, fetchPageContent, extractDomain, isValidUrl } from '../search';

// Mock axios to avoid making real HTTP requests in tests
jest.mock('axios');

describe('Search utilities', () => {
  describe('extractDomain', () => {
    it('should extract domain from valid URLs', () => {
      expect(extractDomain('https://example.com/path')).toBe('example.com');
      expect(extractDomain('http://subdomain.example.com')).toBe('subdomain.example.com');
      expect(extractDomain('https://www.google.com/search?q=test')).toBe('www.google.com');
    });

    it('should return "unknown" for invalid URLs', () => {
      expect(extractDomain('not-a-url')).toBe('unknown');
      expect(extractDomain('')).toBe('unknown');
    });
  });

  describe('isValidUrl', () => {
    it('should validate URLs correctly', () => {
      expect(isValidUrl('https://example.com')).toBe(true);
      expect(isValidUrl('http://example.com')).toBe(true);
      expect(isValidUrl('https://subdomain.example.com/path?query=value')).toBe(true);
      
      expect(isValidUrl('not-a-url')).toBe(false);
      expect(isValidUrl('')).toBe(false);
      expect(isValidUrl('example.com')).toBe(false); // Missing protocol
    });
  });

  describe('searchWeb', () => {
    it('should return search results for valid queries', async () => {
      const results = await searchWeb('test query');
      
      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBeGreaterThan(0);
      
      // Check that each result has required properties
      results.forEach(result => {
        expect(result).toHaveProperty('title');
        expect(result).toHaveProperty('url');
        expect(result).toHaveProperty('snippet');
        expect(typeof result.title).toBe('string');
        expect(typeof result.url).toBe('string');
        expect(typeof result.snippet).toBe('string');
      });
    });

    it('should limit results to 5 items', async () => {
      const results = await searchWeb('popular topic');
      expect(results.length).toBeLessThanOrEqual(5);
    });
  });
});
