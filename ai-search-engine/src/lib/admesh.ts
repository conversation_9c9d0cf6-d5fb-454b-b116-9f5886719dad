import Admesh from 'admesh';

export interface AdmeshRecommendation {
  title: string;
  description: string;
  url?: string;
  category?: string;
  relevanceScore?: number;
}

export interface AdmeshResponse {
  recommendations: AdmeshRecommendation[];
  query: string;
  success: boolean;
  error?: string;
}

class AdmeshService {
  private client: Admesh | null = null;
  private isInitialized = false;

  constructor() {
    this.initializeClient();
  }

  private initializeClient() {
    try {
      const apiKey = process.env.ADMESH_API_KEY;
      
      if (!apiKey) {
        console.warn('ADMESH_API_KEY not found in environment variables');
        return;
      }

      this.client = new Admesh({
        apiKey,
        baseURL: process.env.ADMESH_BASE_URL || 'https://api.useadmesh.com'
      });
      
      this.isInitialized = true;
      console.log('Admesh client initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Admesh client:', error);
      this.isInitialized = false;
    }
  }

  async getRecommendations(query: string): Promise<AdmeshResponse> {
    if (!this.isInitialized || !this.client) {
      return {
        recommendations: [],
        query,
        success: false,
        error: 'Admesh client not initialized. Please check your API key configuration.'
      };
    }

    try {
      console.log(`Getting Admesh recommendations for query: "${query}"`);

      const response = await this.client.recommend.getRecommendations({
        query,
        format: 'auto'
      });

      console.log('Raw Admesh API response:', JSON.stringify(response, null, 2));

      // Transform the response to match our interface
      // Handle different possible response structures
      let rawRecommendations: unknown[] = [];
      const responseAny = response as any; // Type assertion for flexibility

      if (responseAny.response?.recommendations) {
        // Admesh API structure: response.response.recommendations
        rawRecommendations = responseAny.response.recommendations;
      } else if (responseAny.recommendations) {
        rawRecommendations = responseAny.recommendations;
      } else if (Array.isArray(responseAny)) {
        rawRecommendations = responseAny;
      } else if (responseAny.data) {
        rawRecommendations = responseAny.data;
      } else if (responseAny.results) {
        rawRecommendations = responseAny.results;
      } else {
        console.warn('Unknown Admesh response structure:', responseAny);
      }

      const recommendations: AdmeshRecommendation[] = rawRecommendations.map((rec: any) => ({
        title: rec.title || rec.name || 'Untitled Recommendation',
        description: rec.description || rec.reason || rec.snippet || rec.summary || 'No description available',
        url: rec.url || rec.redirect_url || rec.admesh_link,
        category: rec.categories?.[0] || rec.category || rec.type || 'General',
        relevanceScore: rec.intent_match_score || rec.relevanceScore || rec.score || rec.match_score || 0.8
      }));

      console.log(`✅ Successfully received ${recommendations.length} recommendations from Admesh`);
      console.log('Transformed recommendations:', recommendations);
      return {
        recommendations,
        query,
        success: true
      };
    } catch (error) {
      console.error('Error getting Admesh recommendations:', error);
      
      return {
        recommendations: [],
        query,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  // Fallback recommendations when Admesh is not available
  private getFallbackRecommendations(query: string): AdmeshRecommendation[] {
    const fallbackRecommendations = [
      {
        title: `Explore more about "${query}"`,
        description: `Discover comprehensive resources and insights related to ${query}`,
        category: 'General'
      },
      {
        title: `Latest trends in ${query}`,
        description: `Stay updated with the most recent developments and trends`,
        category: 'Trends'
      },
      {
        title: `Best practices for ${query}`,
        description: `Learn proven strategies and methodologies from experts`,
        category: 'Best Practices'
      }
    ];

    return fallbackRecommendations;
  }

  async getRecommendationsWithFallback(query: string): Promise<AdmeshResponse> {
    const result = await this.getRecommendations(query);
    
    // If Admesh fails or returns no recommendations, provide fallback
    if (!result.success || result.recommendations.length === 0) {
      return {
        recommendations: this.getFallbackRecommendations(query),
        query,
        success: true,
        error: result.error ? `Admesh unavailable: ${result.error}. Showing fallback recommendations.` : undefined
      };
    }

    return result;
  }

  isAvailable(): boolean {
    return this.isInitialized && this.client !== null;
  }
}

// Export a singleton instance
export const admeshService = new AdmeshService();
export default admeshService;
