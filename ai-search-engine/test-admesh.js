// Test script to check Admesh integration
require('dotenv').config({ path: '.env.local' });

async function testAdmesh() {
  console.log('🔍 Testing Admesh Integration...\n');
  
  // Check environment variables
  console.log('📋 Environment Check:');
  console.log(`ADMESH_API_KEY: ${process.env.ADMESH_API_KEY ? '✅ Set' : '❌ Not set'}`);
  console.log(`ADMESH_BASE_URL: ${process.env.ADMESH_BASE_URL || 'Not set'}`);
  console.log(`OPENAI_API_KEY: ${process.env.OPENAI_API_KEY ? '✅ Set' : '❌ Not set'}\n`);
  
  if (!process.env.ADMESH_API_KEY) {
    console.log('❌ ADMESH_API_KEY is not set. Please add it to your .env.local file.');
    return;
  }
  
  try {
    // Import the Admesh service
    const { admeshService } = require('./src/lib/admesh.ts');
    
    console.log('🚀 Testing Admesh service...');
    console.log(`Service available: ${admeshService.isAvailable()}`);
    
    // Test with a sample query
    const testQuery = 'enterprise CRM software';
    console.log(`\n🔍 Testing query: "${testQuery}"`);
    
    const result = await admeshService.getRecommendationsWithFallback(testQuery);
    
    console.log('\n📊 Results:');
    console.log(`Success: ${result.success}`);
    console.log(`Number of recommendations: ${result.recommendations.length}`);
    console.log(`Error: ${result.error || 'None'}`);
    
    if (result.recommendations.length > 0) {
      console.log('\n📝 Sample recommendations:');
      result.recommendations.slice(0, 3).forEach((rec, index) => {
        console.log(`${index + 1}. ${rec.title}`);
        console.log(`   Description: ${rec.description}`);
        console.log(`   Category: ${rec.category || 'N/A'}`);
        console.log(`   Score: ${rec.relevanceScore || 'N/A'}`);
        console.log(`   URL: ${rec.url || 'N/A'}\n`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error testing Admesh:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Test direct API call
async function testDirectAPI() {
  console.log('\n🔗 Testing Direct Admesh API Call...');
  
  if (!process.env.ADMESH_API_KEY) {
    console.log('❌ Cannot test direct API - ADMESH_API_KEY not set');
    return;
  }
  
  try {
    const Admesh = require('admesh').default;
    
    const client = new Admesh({
      apiKey: process.env.ADMESH_API_KEY,
      baseURL: process.env.ADMESH_BASE_URL || 'https://api.useadmesh.com'
    });
    
    console.log('📡 Making direct API call...');
    
    const response = await client.recommend.getRecommendations({
      query: 'project management tools',
      format: 'auto'
    });
    
    console.log('✅ Direct API call successful!');
    console.log('Raw response:', JSON.stringify(response, null, 2));
    
  } catch (error) {
    console.error('❌ Direct API call failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run tests
async function runTests() {
  await testAdmesh();
  await testDirectAPI();
  
  console.log('\n🎯 Test completed!');
  console.log('\nNext steps:');
  console.log('1. If tests pass: Your Admesh integration is working! 🎉');
  console.log('2. If tests fail: Check your API key and base URL configuration');
  console.log('3. Try a search query in your app at http://localhost:3001');
}

runTests().catch(console.error);
